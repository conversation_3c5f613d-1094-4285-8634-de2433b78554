import { useState, useMemo, useCallback, useEffect } from "react";
import { View, Text, TouchableOpacity, StyleSheet, Alert } from "react-native";
import { JsonValue, isPlainObject } from "../types";
import { ChevronRight, ChevronDown, Copy } from "lucide-react-native";
import { TypeLegend } from "./TypeLegend";
import { useCopy } from "../../../context/CopyContext";
import { displayValue } from "../../devtools/displayValue";

interface EnhancedDataViewerProps {
  title: string;
  data: JsonValue;
  showTypeFilter?: boolean;
  defaultExpanded?: boolean;
}

// Type color mapping - same as VirtualizedDataExplorer
const getTypeColor = (type: string): string => {
  const colors: { [key: string]: string } = {
    string: "#22D3EE", // Cyan
    number: "#3B82F6", // Blue
    bigint: "#8B5CF6", // Purple
    boolean: "#F59E0B", // Orange
    null: "#6B7280", // Gray
    undefined: "#9CA3AF", // Light gray
    function: "#A855F7", // Magenta
    symbol: "#D946EF", // Hot pink
    date: "#EC4899", // Pink
    error: "#EF4444", // Red
    array: "#10B981", // Green
    object: "#F97316", // Orange-red
  };
  return colors[type] || "#9CA3AF";
};

// Get the type of a value
const getValueType = (value: unknown): string => {
  if (Array.isArray(value)) return "array";
  if (value === null) return "null";
  if (value instanceof Date) return "date";
  if (value instanceof Error) return "error";
  return typeof value;
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: "rgba(255, 255, 255, 0.03)",
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.08)",
    overflow: "hidden",
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: "rgba(255, 255, 255, 0.05)",
  },
  title: {
    color: "#FFFFFF",
    fontSize: 14,
    fontWeight: "500",
  },
  copyAllButton: {
    flexDirection: "row",
    alignItems: "center",
    gap: 6,
    paddingHorizontal: 10,
    paddingVertical: 6,
    backgroundColor: "rgba(139, 92, 246, 0.1)",
    borderRadius: 6,
  },
  copyAllText: {
    color: "#8B5CF6",
    fontSize: 12,
    fontWeight: "500",
  },
  content: {
    padding: 12,
  },
  nodeContainer: {
    marginVertical: 2,
  },
  nodeHeader: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    minHeight: 28,
  },
  nodeInfo: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
    paddingVertical: 4,
  },
  chevronContainer: {
    width: 20,
    alignItems: "center",
  },
  nodeKey: {
    color: "#F3F4F6",
    fontSize: 12,
    fontWeight: "500",
    fontFamily: "monospace",
    marginRight: 8,
  },
  typeBadge: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    marginRight: 6,
  },
  typeText: {
    fontSize: 10,
    fontWeight: "600",
  },
  itemCount: {
    color: "#6B7280",
    fontSize: 11,
    marginRight: 8,
  },
  nodeValue: {
    color: "#D1D5DB",
    fontSize: 11,
    flex: 1,
    fontFamily: "monospace",
  },
  copyButton: {
    padding: 4,
    marginLeft: 8,
  },
  childrenContainer: {
    marginTop: 4,
    paddingLeft: 4,
    borderLeftWidth: 1,
    borderLeftColor: "rgba(255, 255, 255, 0.05)",
  },
});
