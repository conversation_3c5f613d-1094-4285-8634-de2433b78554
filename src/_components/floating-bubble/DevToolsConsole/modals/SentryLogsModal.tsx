import { useState } from "react";
import { BaseFloatingModal } from "../../floatingModal/BaseFloatingModal";
import { SentryLogsContent } from "../sections";
import { View, Text } from "react-native";
import { BackButton } from "../../admin/components/BackButton";
import { ConsoleTransportEntry } from "../../admin/logger/types";

interface SentryLogsModalProps {
  visible: boolean;
  onClose: () => void;
  getSentrySubtitle: () => string;
  onBack?: () => void;
  enableSharedModalDimensions?: boolean;
}

/**
 * Specialized modal for Sentry logs following "Decompose by Responsibility"
 * Single purpose: Display sentry logs in a modal context
 */
export function SentryLogsModal({
  visible,
  onClose,
  onBack,
  enableSharedModalDimensions = false,
}: SentryLogsModalProps) {
  const [selectedEntry, setSelectedEntry] =
    useState<ConsoleTransportEntry | null>(null);
  const [showFilterView, setShowFilterView] = useState(false);

  if (!visible) return null;

  // Handle back navigation - back to list from detail/filter view or back to main menu
  const handleBackPress = () => {
    if (selectedEntry) {
      setSelectedEntry(null);
    } else if (showFilterView) {
      setShowFilterView(false);
    } else if (onBack) {
      onBack();
    }
  };

  const renderHeaderContent = () => (
    <View
      style={{
        flexDirection: "row",
        alignItems: "center",
        flex: 1,
        gap: 12,
        minHeight: 32,
        paddingLeft: 4,
      }}
    >
      {(onBack || selectedEntry || showFilterView) && (
        <BackButton onPress={handleBackPress} color="#FFFFFF" size={16} />
      )}
      <Text
        style={{ color: "#E5E7EB", fontSize: 14, fontWeight: "500", flex: 1 }}
        numberOfLines={1}
      >
        {selectedEntry
          ? "Event Details"
          : showFilterView
            ? "Filters"
            : "Sentry Logs"}
      </Text>
    </View>
  );

  const storagePrefix = enableSharedModalDimensions
    ? "@dev_tools_console_modal"
    : "@sentry_logs_modal";

  return (
    <BaseFloatingModal
      visible={visible}
      onClose={onClose}
      storagePrefix={storagePrefix}
      showToggleButton={true}
      customHeaderContent={renderHeaderContent()}
      headerSubtitle={undefined}
    >
      <SentryLogsContent
        selectedEntry={selectedEntry}
        onSelectEntry={setSelectedEntry}
        showFilterView={showFilterView}
        onShowFilterView={setShowFilterView}
      />
    </BaseFloatingModal>
  );
}
