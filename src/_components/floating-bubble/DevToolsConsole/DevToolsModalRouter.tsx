import { RequiredEnvVar } from "../admin/sections/env-vars/types";
import { SentryLogsModal } from "./modals/SentryLogsModal";
import { EnvVarsModal } from "./modals/EnvVarsModal";

// Available section types for navigation
export type SectionType = "sentry-logs" | "env-vars" | "rn-better-dev-tools";

interface DevToolsModalRouterProps {
  selectedSection: SectionType | null;
  onClose: () => void;
  requiredEnvVars: RequiredEnvVar[];
  _getSentrySubtitle: () => string;
  envVarsSubtitle: string;
  onBack?: () => void;
  enableSharedModalDimensions?: boolean;
}

/**
 * Modal router following "Extract Reusable Logic" principle
 * Single responsibility: Route to appropriate specialized modal based on selection
 * No conditional rendering - each modal handles its own visibility
 */
export function DevToolsModalRouter({
  selectedSection,
  onClose,
  requiredEnvVars,
  _getSentrySubtitle,
  envVarsSubtitle,
  onBack,
  enableSharedModalDimensions = false,
}: DevToolsModalRouterProps) {
  return (
    <>
      <SentryLogsModal
        visible={selectedSection === "sentry-logs"}
        onClose={onClose}
        getSentrySubtitle={_getSentrySubtitle}
        onBack={onBack}
        enableSharedModalDimensions={enableSharedModalDimensions}
      />

      <EnvVarsModal
        visible={selectedSection === "env-vars"}
        onClose={onClose}
        requiredEnvVars={requiredEnvVars}
        _envVarsSubtitle={envVarsSubtitle}
        onBack={onBack}
        enableSharedModalDimensions={enableSharedModalDimensions}
      />
    </>
  );
}
