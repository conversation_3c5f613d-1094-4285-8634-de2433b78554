// Main Sentry logger for capturing events
export { LogLevel, LogType, SentryLogger, sentryLogger } from './index-sentry';

// Test logger for generating sample logs
export { TestLogger, testLogger } from './testLogger';

// Log storage and retrieval
export { add, clearEntries, getEntries } from './logDump';

// Types
export type { ConsoleTransportEntry, Metadata, Transport } from './types';
export { LogLevel as LogLevelEnum, LogType as LogTypeEnum } from './types';
