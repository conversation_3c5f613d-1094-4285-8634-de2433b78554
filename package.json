{"name": "react-native-react-query-devtools", "version": "1.5.1", "description": "React Query Dev Tools for React Native", "main": "dist/bundle.cjs.js", "module": "dist/bundle.esm.js", "files": ["dist"], "types": "dist/types/index.d.ts", "scripts": {"build": "node build.js", "build:yalc": "npm run build && yalc push", "dev": "nodemon --watch src --ext ts,tsx,js,jsx --exec \"npm run build:yalc\"", "yalc:publish": "yalc publish", "yalc:push": "yalc push", "check:jsx": "node check-jsx-transform.js --check", "fix:jsx": "node check-jsx-transform.js --fix", "check:jsx-simple": "./check-jsx-transform.sh", "lint": "eslint src --ext .ts,.tsx,.js,.jsx", "lint:fix": "eslint src --ext .ts,.tsx,.js,.jsx --fix", "type-check": "tsc --noEmit", "type-check:watch": "tsc --noEmit --watch", "check": "npm run type-check && npm run lint", "check:fix": "npm run type-check && npm run lint:fix"}, "repository": {"type": "git", "url": "git+https://github.com/LovesWorking/react-native-react-query-devtools.git"}, "keywords": ["react-query", "tanstack", "state management", "dev tools", "react native", "QA", "debugging"], "author": "LovesWorking (https://github.com/LovesWorking)", "peerDependencies": {"@react-native-async-storage/async-storage": "^1.0.0 || ^2.0.0", "@sentry/react-native": "^5.0.0", "@shopify/flash-list": "^1.6.0", "@tanstack/react-query": "^5.77.2", "react": "^18 || ^19", "react-native": ">=0.78.0", "react-native-gesture-handler": "^2.20.0", "react-native-reanimated": "^3.6.0 || ^4.0.0", "react-native-safe-area-context": "^4.0.0 || ^5.0.0", "react-native-svg": "^15.12.0"}, "peerDependenciesMeta": {"@react-native-async-storage/async-storage": {"optional": true}, "@sentry/react-native": {"optional": true}}, "devDependencies": {"@react-native-async-storage/async-storage": "^2.2.0", "@react-native/eslint-config": "^0.80.2", "@tanstack/react-query": "^5.77.2", "@types/react": "^19.0.1", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "concurrently": "^9.2.0", "esbuild": "^0.25.4", "esbuild-node-externals": "^1.18.0", "eslint": "^8.57.1", "eslint-config-prettier": "^10.1.8", "eslint-plugin-jest": "^29.0.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-compiler": "^19.1.0-rc.2", "eslint-plugin-react-native": "^5.0.0", "eslint-plugin-react-native-a11y": "^3.5.1", "eslint-plugin-sentry": "^2.10.0", "eslint-plugin-simple-import-sort": "^12.1.1", "nodemon": "^3.1.10", "prettier": "^3.6.2", "react": "^19.0.0", "react-native": ">=0.78.0", "react-native-gesture-handler": "^2.27.2", "react-native-reanimated": "^4.0.0", "react-native-safe-area-context": "^5.5.2", "react-native-svg": "^15.12.0", "ts-prune": "^0.10.3", "typescript": "^5.8.3"}, "dependencies": {"fast-deep-equal": "^3.1.3", "lucide-react-native": "^0.526.0", "react-native-json-tree": "^1.5.0"}}